const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../auth');
const storyService = require('../services/story');
const videoStoryService = require('../services/videoStoryService');

const router = express.Router();
const prisma = new PrismaClient();

// Generate a new story
router.post('/generate', authenticateToken, async (req, res) => {
  try {
    const { prompt } = req.body;
    
    if (!prompt || prompt.trim().length === 0) {
      return res.status(400).json({ error: 'Story prompt is required' });
    }

    if (prompt.length > 500) {
      return res.status(400).json({ error: 'Prompt is too long (max 500 characters)' });
    }

    // Check if user has processed videos
    const processedVideos = await prisma.video.count({
      where: {
        userId: req.user.userId,
        transcriptionStatus: 'completed',
        visionStatus: 'completed'
      }
    });

    if (processedVideos === 0) {
      return res.status(400).json({ 
        error: 'No processed videos available. Please upload and process videos first.' 
      });
    }

    // Start video story generation (async)
    videoStoryService.generateVideoStory(req.user.userId, prompt.trim())
      .catch(error => {
        console.error('Video story generation failed:', error);
      });

    res.status(202).json({
      message: 'Story generation started',
      prompt: prompt.trim(),
      status: 'processing'
    });

  } catch (error) {
    console.error('Error starting story generation:', error);
    res.status(500).json({ error: 'Failed to start story generation' });
  }
});

// Get all user stories
router.get('/', authenticateToken, async (req, res) => {
  try {
    const stories = await storyService.getUserStories(req.user.userId);
    res.json(stories);
  } catch (error) {
    console.error('Error fetching stories:', error);
    res.status(500).json({ error: 'Failed to fetch stories' });
  }
});

// Get single story
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const story = await prisma.story.findFirst({
      where: {
        id: req.params.id,
        userId: req.user.userId
      },
      include: {
        storyVideos: {
          include: {
            video: {
              select: {
                id: true,
                title: true,
                url: true,
                thumbnailUrl: true,
                duration: true
              }
            }
          },
          orderBy: { order: 'asc' }
        }
      }
    });

    if (!story) {
      return res.status(404).json({ error: 'Story not found' });
    }

    res.json(story);
  } catch (error) {
    console.error('Error fetching story:', error);
    res.status(500).json({ error: 'Failed to fetch story' });
  }
});

// Delete story
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    await storyService.deleteStory(req.params.id, req.user.userId);
    res.json({ message: 'Story deleted successfully' });
  } catch (error) {
    console.error('Error deleting story:', error);
    if (error.message === 'Story not found') {
      return res.status(404).json({ error: error.message });
    }
    res.status(500).json({ error: 'Failed to delete story' });
  }
});

// Get story generation status
router.get('/:id/status', authenticateToken, async (req, res) => {
  try {
    const story = await prisma.story.findFirst({
      where: {
        id: req.params.id,
        userId: req.user.userId
      },
      select: {
        id: true,
        prompt: true,
        status: true,
        createdAt: true,
        updatedAt: true,
        generatedVideoUrl: true,
        storyVideos: {
          select: {
            order: true,
            startTime: true,
            endTime: true,
            video: {
              select: {
                title: true,
                thumbnailUrl: true
              }
            }
          },
          orderBy: { order: 'asc' }
        }
      }
    });

    if (!story) {
      return res.status(404).json({ error: 'Story not found' });
    }

    res.json({
      id: story.id,
      prompt: story.prompt,
      status: story.status,
      createdAt: story.createdAt,
      updatedAt: story.updatedAt,
      hasVideo: !!story.generatedVideoUrl,
      clipCount: story.storyVideos.length,
      clips: story.storyVideos
    });

  } catch (error) {
    console.error('Error fetching story status:', error);
    res.status(500).json({ error: 'Failed to fetch story status' });
  }
});

// Regenerate story with same prompt
router.post('/:id/regenerate', authenticateToken, async (req, res) => {
  try {
    const existingStory = await prisma.story.findFirst({
      where: {
        id: req.params.id,
        userId: req.user.userId
      }
    });

    if (!existingStory) {
      return res.status(404).json({ error: 'Story not found' });
    }

    // Start regeneration (async)
    storyService.generateStory(req.user.userId, existingStory.prompt)
      .catch(error => {
        console.error('Story regeneration failed:', error);
      });

    res.status(202).json({
      message: 'Story regeneration started',
      prompt: existingStory.prompt,
      status: 'processing'
    });

  } catch (error) {
    console.error('Error regenerating story:', error);
    res.status(500).json({ error: 'Failed to regenerate story' });
  }
});

// Get story suggestions based on user's videos
router.get('/suggestions/prompts', authenticateToken, async (req, res) => {
  try {
    // Get user's most common tags
    const topTags = await prisma.tag.findMany({
      where: {
        video: {
          userId: req.user.userId
        }
      },
      select: {
        label: true,
        type: true
      },
      orderBy: {
        confidence: 'desc'
      },
      take: 10
    });

    // Generate prompt suggestions based on tags
    const suggestions = [];
    
    if (topTags.some(tag => tag.type === 'activity')) {
      suggestions.push('Create an action-packed adventure story');
    }
    
    if (topTags.some(tag => tag.type === 'emotion')) {
      suggestions.push('Tell an emotional journey story');
    }
    
    if (topTags.some(tag => tag.type === 'scene')) {
      suggestions.push('Create a travel documentary');
    }

    // Default video story suggestions
    suggestions.push(
      'Make a story of my Goa trip',
      'Show my trekking moments',
      'Create a day in the life video story',
      'Make a before and after transformation video',
      'Tell a story of my growth and learning journey',
      'Create a behind-the-scenes documentary',
      'Make a motivational video story',
      'Show my cooking adventures',
      'Create a fitness journey video'
    );

    res.json({
      suggestions: suggestions.slice(0, 5),
      basedOnTags: topTags.slice(0, 5)
    });

  } catch (error) {
    console.error('Error generating suggestions:', error);
    res.status(500).json({ error: 'Failed to generate suggestions' });
  }
});

module.exports = router;
