const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../auth');

const router = express.Router();
const prisma = new PrismaClient();

// Get all videos for authenticated user
router.get('/', authenticateToken, async (req, res) => {
  try {
    const videos = await prisma.video.findMany({
      where: { userId: req.user.userId },
      include: {
        tags: {
          select: {
            id: true,
            label: true,
            confidence: true,
            type: true,
            timestamp: true
          }
        },
        transcript: {
          select: {
            id: true,
            fullText: true,
            segments: true,
            createdAt: true
          }
        }
      },
      orderBy: { uploadDate: 'desc' }
    });

    res.json(videos);
  } catch (error) {
    console.error('Error fetching videos:', error);
    res.status(500).json({ error: 'Failed to fetch videos' });
  }
});

// Get single video by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const video = await prisma.video.findFirst({
      where: {
        id: req.params.id,
        userId: req.user.userId
      },
      include: {
        tags: {
          select: {
            id: true,
            label: true,
            confidence: true,
            type: true,
            timestamp: true
          }
        },
        transcript: {
          select: {
            id: true,
            fullText: true,
            segments: true,
            createdAt: true
          }
        }
      }
    });

    if (!video) {
      return res.status(404).json({ error: 'Video not found' });
    }

    res.json(video);
  } catch (error) {
    console.error('Error fetching video:', error);
    res.status(500).json({ error: 'Failed to fetch video' });
  }
});

// Update video metadata
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { title, description } = req.body;
    
    const video = await prisma.video.findFirst({
      where: {
        id: req.params.id,
        userId: req.user.userId
      }
    });

    if (!video) {
      return res.status(404).json({ error: 'Video not found' });
    }

    const updatedVideo = await prisma.video.update({
      where: { id: req.params.id },
      data: {
        ...(title && { title }),
        ...(description && { description })
      },
      include: {
        tags: {
          select: {
            id: true,
            label: true,
            confidence: true,
            type: true,
            timestamp: true
          }
        }
      }
    });

    res.json(updatedVideo);
  } catch (error) {
    console.error('Error updating video:', error);
    res.status(500).json({ error: 'Failed to update video' });
  }
});

// Delete video
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const video = await prisma.video.findFirst({
      where: {
        id: req.params.id,
        userId: req.user.userId
      }
    });

    if (!video) {
      return res.status(404).json({ error: 'Video not found' });
    }

    // Delete video and related data (cascading delete handles tags)
    await prisma.video.delete({
      where: { id: req.params.id }
    });

    res.json({ message: 'Video deleted successfully' });
  } catch (error) {
    console.error('Error deleting video:', error);
    res.status(500).json({ error: 'Failed to delete video' });
  }
});

// Search videos
router.get('/search', authenticateToken, async (req, res) => {
  try {
    const { query } = req.query;
    
    if (!query) {
      return res.status(400).json({ error: 'Search query is required' });
    }

    // Search in video titles, descriptions, transcripts, and tags
    const videos = await prisma.video.findMany({
      where: {
        userId: req.user.userId,
        OR: [
          { title: { contains: query, mode: 'insensitive' } },
          { description: { contains: query, mode: 'insensitive' } },
          { transcript: { contains: query, mode: 'insensitive' } },
          {
            tags: {
              some: {
                label: { contains: query, mode: 'insensitive' }
              }
            }
          }
        ]
      },
      include: {
        tags: {
          select: {
            id: true,
            label: true,
            confidence: true,
            type: true,
            timestamp: true
          }
        }
      },
      orderBy: { uploadDate: 'desc' }
    });

    res.json(videos);
  } catch (error) {
    console.error('Error searching videos:', error);
    res.status(500).json({ error: 'Failed to search videos' });
  }
});

module.exports = router;
