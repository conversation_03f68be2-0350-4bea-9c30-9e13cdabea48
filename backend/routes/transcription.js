const express = require('express');
const router = express.Router();
const transcriptionService = require('../services/simpleTranscriptionService');
const { authenticateToken } = require('../auth');

// Transcribe a specific video
router.post('/transcribe/:videoId', authenticateToken, async (req, res) => {
  try {
    const { videoId } = req.params;
    const { language = 'original' } = req.body; // 'original' or 'english'

    // Get video details
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();

    const video = await prisma.video.findUnique({
      where: { id: videoId }
    });

    if (!video) {
      return res.status(404).json({
        success: false,
        error: 'Video not found'
      });
    }

    // Check if user owns the video
    if (video.userId !== req.user.userId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    // Allow re-transcription with different language
    if (video.transcriptionStatus === 'completed' && language !== 'original') {
      // Delete existing transcript for re-transcription
      await prisma.transcript.deleteMany({
        where: { videoId }
      });
    }

    // Start transcription with language option
    const result = await transcriptionService.processVideoTranscription(videoId, video.url, language);

    res.json({
      success: true,
      message: 'Transcription completed',
      language: language,
      transcript: result
    });

  } catch (error) {
    console.error('Transcription route error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Transcription failed'
    });
  }
});

// Get transcript for a video
router.get('/transcript/:videoId', authenticateToken, async (req, res) => {
  try {
    const { videoId } = req.params;
    
    // Get video details to check ownership
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();
    
    const video = await prisma.video.findUnique({
      where: { id: videoId }
    });

    if (!video) {
      return res.status(404).json({
        success: false,
        error: 'Video not found'
      });
    }

    // Check if user owns the video
    if (video.userId !== req.user.userId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    const transcript = await transcriptionService.getTranscriptionStatus(videoId);

    if (!transcript) {
      return res.status(404).json({
        success: false,
        error: 'Transcript not found'
      });
    }

    res.json({
      success: true,
      transcript
    });

  } catch (error) {
    console.error('Get transcript route error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get transcript'
    });
  }
});

// Search transcripts
router.get('/search', authenticateToken, async (req, res) => {
  try {
    const { query } = req.query;

    if (!query || query.trim().length < 2) {
      return res.status(400).json({
        success: false,
        error: 'Search query must be at least 2 characters long'
      });
    }

    const results = await transcriptionService.searchTranscripts(query.trim());

    // Filter results to only include videos owned by the user
    const userResults = results.filter(result => 
      result.video && result.video.userId === req.user.userId
    );

    res.json({
      success: true,
      results: userResults,
      query: query.trim()
    });

  } catch (error) {
    console.error('Search transcripts route error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Search failed'
    });
  }
});

// Get transcription status for multiple videos
router.post('/status', authenticateToken, async (req, res) => {
  try {
    const { videoIds } = req.body;

    if (!Array.isArray(videoIds)) {
      return res.status(400).json({
        success: false,
        error: 'videoIds must be an array'
      });
    }

    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();

    const videos = await prisma.video.findMany({
      where: {
        id: { in: videoIds },
        userId: req.user.userId
      },
      select: {
        id: true,
        transcriptionStatus: true,
        transcript: {
          select: {
            id: true,
            createdAt: true
          }
        }
      }
    });

    const statusMap = {};
    videos.forEach(video => {
      statusMap[video.id] = {
        status: video.transcriptionStatus,
        hasTranscript: !!video.transcript,
        transcriptCreatedAt: video.transcript?.createdAt
      };
    });

    res.json({
      success: true,
      statuses: statusMap
    });

  } catch (error) {
    console.error('Get transcription status route error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get transcription status'
    });
  }
});

module.exports = router;
