const express = require('express');
const multer = require('multer');
const { v2: cloudinary } = require('cloudinary');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../auth');
const transcriptionService = require('../services/simpleTranscriptionService');
const visionService = require('../services/simpleVisionService');

const router = express.Router();
const prisma = new PrismaClient();

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
  timeout: 300000, // 5 minutes timeout
});

// Configure multer for memory storage
const storage = multer.memoryStorage();
const upload = multer({ 
  storage,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept video files
    if (file.mimetype.startsWith('video/')) {
      cb(null, true);
    } else {
      cb(new Error('Only video files are allowed'), false);
    }
  }
});

// Upload video endpoint
router.post('/', authenticateToken, upload.single('video'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No video file provided' });
    }

    const { title, description } = req.body;

    console.log('Uploading video to Cloudinary...');
    
    // Upload to Cloudinary with timeout handling
    const uploadResult = await new Promise((resolve, reject) => {
      // Set a timeout for the upload
      const timeout = setTimeout(() => {
        reject(new Error('Upload timeout - please try again with a smaller file or check your connection'));
      }, 300000); // 5 minutes

      const uploadStream = cloudinary.uploader.upload_stream(
        {
          resource_type: 'video',
          folder: 'footage-flow',
          public_id: `video_${Date.now()}`,
          eager: [
            { width: 300, height: 200, crop: 'fill', format: 'jpg' } // Generate thumbnail
          ],
          chunk_size: 6000000, // 6MB chunks for better reliability
          timeout: 300000 // 5 minutes timeout
        },
        (error, result) => {
          clearTimeout(timeout);
          if (error) {
            console.error('Cloudinary upload error:', error);

            // Handle specific Cloudinary errors
            if (error.http_code === 499 || error.name === 'TimeoutError') {
              reject(new Error('Upload timeout - please try again with a smaller file or check your connection'));
            } else if (error.http_code === 413) {
              reject(new Error('File too large - please use a file smaller than 100MB'));
            } else if (error.http_code === 400) {
              reject(new Error('Invalid file format - please upload a valid video file'));
            } else {
              reject(new Error(`Upload failed: ${error.message || 'Unknown error'}`));
            }
          } else {
            resolve(result);
          }
        }
      );

      uploadStream.end(req.file.buffer);
    });

    console.log('Video uploaded to Cloudinary:', uploadResult.public_id);

    // Save video metadata to database
    const video = await prisma.video.create({
      data: {
        userId: req.user.userId,
        title: title || `Video ${new Date().toLocaleDateString()}`,
        description: description || '',
        url: uploadResult.secure_url,
        cloudinaryId: uploadResult.public_id,
        duration: uploadResult.duration,
        thumbnailUrl: uploadResult.eager?.[0]?.secure_url || uploadResult.secure_url,
        transcriptionStatus: 'pending',
        visionStatus: 'pending'
      }
    });

    console.log('Video metadata saved to database:', video.id);

    // Start AI processing in background (don't wait for completion)
    setTimeout(() => {
      Promise.all([
        transcriptionService.autoTranscribeVideo(video.id, video.url).catch(console.error),
        visionService.autoAnalyzeVideo(video.id, video.url).catch(console.error)
      ]);
    }, 1000); // Small delay to ensure response is sent first

    res.status(201).json({
      message: 'Video uploaded successfully',
      video: {
        id: video.id,
        title: video.title,
        description: video.description,
        url: video.url,
        thumbnailUrl: video.thumbnailUrl,
        duration: video.duration,
        uploadDate: video.uploadDate,
        transcriptionStatus: video.transcriptionStatus,
        visionStatus: video.visionStatus
      }
    });

  } catch (error) {
    console.error('Upload error:', error);
    
    if (error.message === 'Only video files are allowed') {
      return res.status(400).json({ error: error.message });
    }
    
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: 'File size too large. Maximum size is 100MB.' });
    }

    res.status(500).json({ 
      error: 'Failed to upload video',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Get upload status
router.get('/status/:videoId', authenticateToken, async (req, res) => {
  try {
    const video = await prisma.video.findFirst({
      where: {
        id: req.params.videoId,
        userId: req.user.userId
      },
      select: {
        id: true,
        title: true,
        transcriptionStatus: true,
        visionStatus: true,
        uploadDate: true
      }
    });

    if (!video) {
      return res.status(404).json({ error: 'Video not found' });
    }

    res.json(video);
  } catch (error) {
    console.error('Error fetching upload status:', error);
    res.status(500).json({ error: 'Failed to fetch upload status' });
  }
});

module.exports = router;
