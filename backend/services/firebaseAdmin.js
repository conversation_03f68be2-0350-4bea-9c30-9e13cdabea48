const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  // For development, we'll use a simplified approach
  // In production, you should use proper service account credentials
  admin.initializeApp({
    projectId: process.env.FIREBASE_PROJECT_ID || 'footageflow-151b6',
    // For now, we'll handle authentication without service account
    // This will work for ID token verification
  });
}

class FirebaseAdminService {
  constructor() {
    this.auth = admin.auth();
  }

  // Verify Firebase ID token
  async verifyIdToken(idToken) {
    try {
      const decodedToken = await this.auth.verifyIdToken(idToken);
      return {
        uid: decodedToken.uid,
        email: decodedToken.email,
        name: decodedToken.name,
        picture: decodedToken.picture,
        emailVerified: decodedToken.email_verified,
        provider: decodedToken.firebase.sign_in_provider
      };
    } catch (error) {
      console.error('Error verifying Firebase ID token:', error);
      throw new Error('Invalid Firebase token');
    }
  }

  // Get user by UID
  async getUserByUid(uid) {
    try {
      const userRecord = await this.auth.getUser(uid);
      return {
        uid: userRecord.uid,
        email: userRecord.email,
        name: userRecord.displayName,
        picture: userRecord.photoURL,
        emailVerified: userRecord.emailVerified,
        provider: userRecord.providerData[0]?.providerId || 'firebase'
      };
    } catch (error) {
      console.error('Error getting user by UID:', error);
      throw new Error('User not found');
    }
  }

  // Create custom token (if needed)
  async createCustomToken(uid, additionalClaims = {}) {
    try {
      return await this.auth.createCustomToken(uid, additionalClaims);
    } catch (error) {
      console.error('Error creating custom token:', error);
      throw new Error('Failed to create custom token');
    }
  }
}

module.exports = new FirebaseAdminService();
