const sgMail = require('@sendgrid/mail');
const crypto = require('crypto');

// Initialize SendGrid
sgMail.setApiKey(process.env.SENDGRID_API_KEY);

class EmailService {
  constructor() {
    this.otpStore = new Map(); // In production, use Redis or database
  }

  // Generate OTP
  generateOTP() {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  // Store OTP with expiration
  storeOTP(email, otp) {
    const expiresAt = Date.now() + 10 * 60 * 1000; // 10 minutes
    this.otpStore.set(email, { otp, expiresAt });
    
    // Clean up expired OTPs
    setTimeout(() => {
      this.otpStore.delete(email);
    }, 10 * 60 * 1000);
  }

  // Verify OTP
  verifyOTP(email, otp) {
    const stored = this.otpStore.get(email);
    if (!stored) {
      throw new Error('OTP not found or expired');
    }

    if (Date.now() > stored.expiresAt) {
      this.otpStore.delete(email);
      throw new Error('OTP has expired');
    }

    if (stored.otp !== otp) {
      throw new Error('Invalid OTP');
    }

    // OTP is valid, remove it
    this.otpStore.delete(email);
    return true;
  }

  // Send OTP email
  async sendOTP(email, purpose = 'password reset') {
    try {
      const otp = this.generateOTP();
      this.storeOTP(email, otp);

      const msg = {
        to: email,
        from: {
          email: process.env.SENDGRID_FROM_EMAIL,
          name: process.env.SENDGRID_FROM_NAME
        },
        subject: `Your FootageFlow ${purpose} OTP`,
        html: this.getOTPEmailTemplate(otp, purpose)
      };

      await sgMail.send(msg);
      console.log(`✅ OTP sent to ${email} for ${purpose}`);
      
      return { success: true, message: 'OTP sent successfully' };
    } catch (error) {
      console.error('❌ Failed to send OTP:', error);
      throw new Error('Failed to send OTP email');
    }
  }

  // Send welcome email for Google OAuth users
  async sendWelcomeEmail(email, name) {
    try {
      // Check if SendGrid is properly configured
      if (!process.env.SENDGRID_API_KEY || !process.env.SENDGRID_FROM_EMAIL) {
        console.log('⚠️ SendGrid not configured, skipping welcome email');
        return { success: false, message: 'Email service not configured' };
      }

      const msg = {
        to: email,
        from: {
          email: process.env.SENDGRID_FROM_EMAIL,
          name: process.env.SENDGRID_FROM_NAME
        },
        subject: 'Welcome to FootageFlow!',
        html: this.getWelcomeEmailTemplate(name)
      };

      await sgMail.send(msg);
      console.log(`✅ Welcome email sent to ${email}`);

      return { success: true, message: 'Welcome email sent successfully' };
    } catch (error) {
      console.error('❌ Failed to send welcome email:', error);

      // Handle specific SendGrid errors
      if (error.code === 401) {
        console.error('❌ SendGrid API key is invalid or sender email is not verified');
        console.log('💡 To fix: Verify your sender email in SendGrid dashboard');
      } else if (error.code === 403) {
        console.error('❌ SendGrid sender email not verified or insufficient permissions');
      }

      // Don't throw error for welcome email failure
      return { success: false, message: 'Failed to send welcome email' };
    }
  }

  // Send password setup notification
  async sendPasswordSetupNotification(email, name) {
    try {
      const msg = {
        to: email,
        from: {
          email: process.env.SENDGRID_FROM_EMAIL,
          name: process.env.SENDGRID_FROM_NAME
        },
        subject: 'Password Setup Complete - FootageFlow',
        html: this.getPasswordSetupTemplate(name)
      };

      await sgMail.send(msg);
      console.log(`✅ Password setup notification sent to ${email}`);
      
      return { success: true, message: 'Password setup notification sent successfully' };
    } catch (error) {
      console.error('❌ Failed to send password setup notification:', error);
      return { success: false, message: 'Failed to send password setup notification' };
    }
  }

  // Send password reset OTP
  async sendPasswordResetOTP(email, name, otp) {
    try {
      // Check if SendGrid is properly configured
      if (!process.env.SENDGRID_API_KEY || !process.env.SENDGRID_FROM_EMAIL) {
        console.log('⚠️ SendGrid not configured, skipping password reset email');
        return { success: false, message: 'Email service not configured' };
      }

      const msg = {
        to: email,
        from: {
          email: process.env.SENDGRID_FROM_EMAIL,
          name: process.env.SENDGRID_FROM_NAME
        },
        subject: 'Password Reset - FootageFlow',
        html: this.getPasswordResetTemplate(name, otp)
      };

      await sgMail.send(msg);
      console.log(`✅ Password reset OTP sent to ${email}`);

      return { success: true, message: 'Password reset OTP sent successfully' };
    } catch (error) {
      console.error('❌ Failed to send password reset OTP:', error);

      // Handle specific SendGrid errors
      if (error.code === 401) {
        console.error('❌ SendGrid API key is invalid or sender email is not verified');
        console.log('💡 To fix: Verify your sender email in SendGrid dashboard');
      } else if (error.code === 403) {
        console.error('❌ SendGrid sender email not verified or insufficient permissions');
      }

      // Don't throw error for email failure - return success anyway for security
      return { success: true, message: 'If the email exists, a reset code has been sent' };
    }
  }

  // OTP Email Template
  getOTPEmailTemplate(otp, purpose) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>FootageFlow OTP</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #3b82f6, #8b5cf6); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px; }
          .otp-box { background: white; border: 2px solid #3b82f6; border-radius: 10px; padding: 20px; text-align: center; margin: 20px 0; }
          .otp-code { font-size: 32px; font-weight: bold; color: #3b82f6; letter-spacing: 5px; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>FootageFlow</h1>
            <p>Your ${purpose} verification code</p>
          </div>
          <div class="content">
            <p>Hello,</p>
            <p>You requested a ${purpose} for your FootageFlow account. Please use the following OTP to proceed:</p>
            
            <div class="otp-box">
              <div class="otp-code">${otp}</div>
              <p style="margin: 10px 0 0 0; color: #666;">This code expires in 10 minutes</p>
            </div>
            
            <p>If you didn't request this ${purpose}, please ignore this email or contact our support team.</p>
            
            <div class="footer">
              <p>Best regards,<br>The FootageFlow Team</p>
              <p style="font-size: 12px; color: #999;">
                Maa Kripa, Shakambhari Avenue<br>
                Behind SAIMS Hospital, Bhawrasala<br>
                Indore, 453111 IND
              </p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Welcome Email Template
  getWelcomeEmailTemplate(name) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to FootageFlow</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #3b82f6, #8b5cf6); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px; }
          .feature-box { background: white; border-left: 4px solid #3b82f6; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .cta-button { display: inline-block; background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Welcome to FootageFlow!</h1>
            <p>Your video storytelling journey begins now</p>
          </div>
          <div class="content">
            <p>Hi ${name},</p>
            <p>Welcome to FootageFlow! We're excited to have you join our community of video creators and storytellers.</p>
            
            <div class="feature-box">
              <h3>🎥 Upload & Store</h3>
              <p>Securely upload your videos to the cloud with automatic processing</p>
            </div>
            
            <div class="feature-box">
              <h3>🔍 Smart Search</h3>
              <p>Find specific moments in your videos using AI-powered search</p>
            </div>
            
            <div class="feature-box">
              <h3>✨ AI Stories</h3>
              <p>Generate compelling stories from your video content automatically</p>
            </div>
            
            <p>To complete your account setup, please set up a password so you can also login with your email and password in the future.</p>
            
            <a href="${process.env.FRONTEND_URL}/dashboard" class="cta-button">Get Started</a>
            
            <div class="footer">
              <p>Best regards,<br>The FootageFlow Team</p>
              <p style="font-size: 12px; color: #999;">
                Maa Kripa, Shakambhari Avenue<br>
                Behind SAIMS Hospital, Bhawrasala<br>
                Indore, 453111 IND
              </p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Password Setup Template
  getPasswordSetupTemplate(name) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Password Setup Complete</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #10b981, #3b82f6); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px; }
          .success-box { background: #d1fae5; border: 2px solid #10b981; border-radius: 10px; padding: 20px; text-align: center; margin: 20px 0; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>✅ Password Setup Complete</h1>
            <p>Your FootageFlow account is now fully secured</p>
          </div>
          <div class="content">
            <p>Hi ${name},</p>
            
            <div class="success-box">
              <h3 style="color: #10b981; margin: 0;">Password Successfully Set!</h3>
              <p style="margin: 10px 0 0 0;">You can now login using either Google OAuth or your email and password.</p>
            </div>
            
            <p>Your account now supports multiple login methods:</p>
            <ul>
              <li><strong>Google OAuth:</strong> Quick and secure login with your Google account</li>
              <li><strong>Email & Password:</strong> Traditional login with your email and the password you just set</li>
            </ul>
            
            <p>If you have any questions or need assistance, feel free to reach out to our support team.</p>
            
            <div class="footer">
              <p>Best regards,<br>The FootageFlow Team</p>
              <p style="font-size: 12px; color: #999;">
                Maa Kripa, Shakambhari Avenue<br>
                Behind SAIMS Hospital, Bhawrasala<br>
                Indore, 453111 IND
              </p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Password Reset Email Template
  getPasswordResetTemplate(name, otp) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Password Reset - FootageFlow</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #ef4444, #f97316); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px; }
          .otp-box { background: white; border: 2px solid #ef4444; border-radius: 10px; padding: 20px; text-align: center; margin: 20px 0; }
          .otp-code { font-size: 32px; font-weight: bold; color: #ef4444; letter-spacing: 5px; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>FootageFlow</h1>
            <h2>Password Reset Request</h2>
          </div>
          <div class="content">
            <p>Hello ${name || 'User'},</p>
            <p>We received a request to reset your password. Use the verification code below to reset your password:</p>

            <div class="otp-box">
              <div class="otp-code">${otp}</div>
              <p style="margin: 10px 0 0 0; color: #666; font-size: 14px;">This code expires in 10 minutes</p>
            </div>

            <p>If you didn't request this password reset, please ignore this email. Your password will remain unchanged.</p>

            <div class="footer">
              <p>This is an automated message from FootageFlow. Please do not reply to this email.</p>
              <p>&copy; 2024 FootageFlow. All rights reserved.</p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}

module.exports = new EmailService();
