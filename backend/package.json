{"name": "footage-flow-backend", "version": "1.0.0", "description": "Backend for Footage Flow MVP", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "db:push": "npx prisma db push", "db:generate": "npx prisma generate"}, "dependencies": {"@google-cloud/vision": "^4.3.3", "@prisma/client": "^5.7.1", "@sendgrid/mail": "^8.1.5", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "cloudinary": "^1.41.0", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-session": "^1.17.3", "firebase-admin": "^13.4.0", "fluent-ffmpeg": "^2.1.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "openai": "^4.104.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "prisma": "^5.7.1", "validator": "^13.11.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["video", "ai", "transcription", "story-generation"], "author": "Footage Flow Team", "license": "MIT"}