# Database
DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"

# Firebase Configuration
FIREBASE_PROJECT_ID="footageflow-151b6"
FIREBASE_CLIENT_EMAIL="<EMAIL>"
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----"

# JWT
JWT_SECRET="your-super-secret-jwt-key-change-in-production"

# Cloudinary
CLOUDINARY_URL="cloudinary://618132839939623:7T4S9KWaBOTnulm2vNc_FX1rQNE@do4n6dmls"
CLOUDINARY_CLOUD_NAME="do4n6dmls"
CLOUDINARY_API_KEY="618132839939623"
CLOUDINARY_API_SECRET="7T4S9KWaBOTnulm2vNc_FX1rQNE"

# AI Services
GEMINI_API_KEY="AIzaSyBBXdO8fhegyZDCyHIxbcBZng1WWQ4mS2c"
ASSEMBLYAI_API_KEY="********************************"
CLARIFAI_API_KEY="********************************"

# Server
PORT=5174
NODE_ENV=development

# Frontend URL
FRONTEND_URL="http://localhost:5173"

# SendGrid Email
SENDGRID_API_KEY="*********************************************************************"
SENDGRID_FROM_EMAIL="<EMAIL>"
SENDGRID_FROM_NAME="Footage Flow"
