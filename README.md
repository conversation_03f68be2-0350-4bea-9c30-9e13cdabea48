# FootageFlow MVP

A comprehensive web-based video management application with AI-powered transcription, vision analysis, and story generation capabilities. Built with modern web technologies and enhanced with beautiful animations and interactive UI elements.

## ✨ Features

### 🔐 Authentication & Security
- **Google OAuth2 Integration**: Secure login with Google accounts
- **JWT Token Management**: Persistent authentication with automatic refresh
- **Protected Routes**: Secure access to user content

### 📤 Video Management
- **Drag & Drop Upload**: Intuitive video upload with progress tracking
- **Cloudinary Integration**: Reliable cloud storage with automatic thumbnails
- **Video Processing**: Automatic AI analysis pipeline
- **Metadata Management**: Edit titles, descriptions, and organize content

### 🤖 AI-Powered Features
- **Smart Transcription**: AssemblyAI integration with speaker detection
- **Vision Analysis**: Gemini Vision API for automatic tagging
- **Content Recognition**: Objects, scenes, activities, emotions, and text detection
- **Natural Language Search**: Find videos using conversational queries
- **Story Generation**: AI-powered narrative creation from video clips

### 🎨 Enhanced UI/UX
- **Smooth Animations**: Framer Motion powered transitions
- **Spotlight Effects**: Dynamic lighting on interactive elements
- **Gradient Hover Effects**: Beautiful button and card interactions
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Interactive Components**: Ripple effects, floating animations, and micro-interactions

## Tech Stack

### Backend
- Node.js with Express.js
- PostgreSQL (Neon.tech)
- Prisma ORM
- Google OAuth2 + JWT
- Cloudinary for video storage
- OpenAI GPT-4, Whisper
- Google Cloud Vision API
- AssemblyAI

### Frontend
- React with TypeScript
- Tailwind CSS
- Framer Motion
- React Router
- Axios

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm 8+
- Modern web browser (Chrome, Firefox, Safari, Edge)

### Option 1: Automated Setup (Recommended)

**Windows:**
```bash
# Run the complete setup and test script
start-and-test.bat
```

This will:
- ✅ Check prerequisites
- 📦 Install all dependencies
- 🗄️ Setup database
- 🚀 Start both servers
- 🧪 Run API tests
- 🌐 Open browser automatically

### Option 2: Manual Setup

**1. Install root dependencies:**
```bash
npm install
```

**2. Setup and start everything:**
```bash
npm run setup
npm run dev
```

**3. Or step by step:**
```bash
# Backend setup
cd backend
npm install
npx prisma db push
npx prisma generate
npm run dev

# Frontend setup (in new terminal)
cd frontend
npm install
npm start
```

### 🧪 Testing

**Test API endpoints:**
```bash
npm test
# or
node test-api.js
```

**Access the application:**
- Frontend: http://localhost:5173
- Backend: http://localhost:5174
- Health Check: http://localhost:5174/health

## Environment Variables

### Backend (.env)
```
DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"
GOOGLE_CLIENT_ID="868268917843-thq0abbhemfb1goi119pi3507gk0hftp.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
CLOUDINARY_URL="cloudinary://618132839939623:7T4S9KWaBOTnulm2vNc_FX1rQNE@do4n6dmls"
CLOUDINARY_CLOUD_NAME="do4n6dmls"
CLOUDINARY_API_KEY="618132839939623"
CLOUDINARY_API_SECRET="7T4S9KWaBOTnulm2vNc_FX1rQNE"
GEMINI_API_KEY="AIzaSyBBXdO8fhegyZDCyHIxbcBZng1WWQ4mS2c"
ASSEMBLYAI_API_KEY="********************************"
PORT=5174
NODE_ENV=development
FRONTEND_URL="http://localhost:5173"
```

### Frontend (.env)
```
REACT_APP_API_URL=http://localhost:5174
GENERATE_SOURCEMAP=false
```

## 📡 API Endpoints

### Authentication
- `GET /auth/google` - Initiate Google OAuth
- `GET /auth/google/callback` - OAuth callback
- `POST /auth/logout` - Logout user
- `GET /auth/me` - Get current user info

### Videos
- `GET /api/videos` - Get user videos with tags
- `GET /api/videos/:id` - Get single video details
- `PUT /api/videos/:id` - Update video metadata
- `DELETE /api/videos/:id` - Delete video and related data
- `GET /api/videos/search?query=...` - Basic video search

### Upload
- `POST /api/upload` - Upload video (triggers AI processing)
- `GET /api/upload/status/:videoId` - Get upload status

### AI Processing
- `POST /api/ai/transcribe/:videoId` - Start transcription
- `POST /api/ai/analyze/:videoId` - Start vision analysis
- `POST /api/ai/process/:videoId` - Start both transcription and vision
- `GET /api/ai/status/:videoId` - Get processing status
- `GET /api/ai/search?query=...&type=...` - Enhanced AI search
- `GET /api/ai/tags/stats` - Get tag statistics

### Stories
- `POST /api/stories/generate` - Generate new story
- `GET /api/stories` - Get user stories
- `GET /api/stories/:id` - Get single story
- `DELETE /api/stories/:id` - Delete story
- `GET /api/stories/:id/status` - Get story generation status
- `POST /api/stories/:id/regenerate` - Regenerate story
- `GET /api/stories/suggestions/prompts` - Get story prompt suggestions

## 🎯 Implementation Status

### ✅ Day 1: Foundation (Complete)
- [x] Backend setup with Express and Prisma
- [x] Database schema and connection
- [x] Google OAuth authentication with JWT
- [x] Video upload with Cloudinary integration
- [x] Frontend React setup with TypeScript
- [x] Authentication UI and protected routes
- [x] Video upload component with drag & drop
- [x] Dashboard with video listing and stats

### ✅ Day 2: AI Integration (Complete)
- [x] Transcription service (AssemblyAI)
- [x] Vision analysis (Gemini Vision API)
- [x] Enhanced search functionality with filters
- [x] AI processing endpoints and automation
- [x] Tag generation and categorization
- [x] Real-time processing status updates

### ✅ Day 3: Story Generation & Polish (Complete)
- [x] Story generation with Gemini AI
- [x] Advanced UI animations and effects
- [x] Spotlight and gradient hover effects
- [x] Interactive components with micro-animations
- [x] Comprehensive testing suite
- [x] Complete documentation and setup scripts

### 🎨 UI Enhancements Added
- [x] Framer Motion animations throughout
- [x] Spotlight effects on key components
- [x] Gradient hover effects on buttons
- [x] Ripple click effects
- [x] Floating animations
- [x] Enhanced search with real-time results
- [x] Story generator with AI suggestions
- [x] Responsive design with mobile support

## Design System

### Colors
- Primary: `#FF5A5F` (buttons, CTAs)
- Primary Hover: `#FF7C82`
- Neutral 900: `#0F0F11` (text)
- Neutral 700: `#3C3C43` (secondary text)
- Neutral 300: `#D1D1D6` (borders)
- Neutral 100: `#F7F7F9` (cards)
- Background: `#FFFFFF`

### Typography
- Font: Inter
- Headings: 600 weight
- Body: 400 weight
- Captions: 500 weight

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details
