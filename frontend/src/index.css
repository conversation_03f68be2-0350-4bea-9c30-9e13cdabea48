@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  * {
    @apply border-neutral-300;
  }
  
  body {
    @apply text-neutral-900 font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
    background: transparent;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold;
    line-height: 110%;
  }
  
  h1 {
    @apply text-4xl md:text-5xl;
  }
  
  h2 {
    @apply text-3xl md:text-4xl;
  }
  
  h3 {
    @apply text-2xl md:text-3xl;
  }
  
  p {
    @apply text-base md:text-lg;
    line-height: 150%;
  }
  
  .caption {
    @apply text-sm font-medium;
    line-height: 140%;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-500 text-white px-6 py-3 rounded-full font-medium transition-all duration-300 hover:bg-primary-400 hover:scale-105 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 relative overflow-hidden;
  }

  .btn-primary::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-primary-400 to-primary-500 opacity-0 transition-opacity duration-300;
  }

  .btn-primary:hover::before {
    @apply opacity-100;
  }

  .btn-secondary {
    @apply bg-neutral-100 text-neutral-900 px-6 py-3 rounded-full font-medium transition-all duration-300 hover:bg-neutral-200 hover:scale-105 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-neutral-500 focus:ring-offset-2;
  }

  .btn-ghost {
    @apply bg-transparent text-primary-500 px-6 py-3 rounded-full font-medium transition-all duration-300 hover:bg-primary-50 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-icon {
    @apply p-3 rounded-full transition-all duration-300 hover:bg-neutral-100 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .card {
    @apply bg-white/80 backdrop-blur-sm rounded-lg shadow-sm p-6 transition-all duration-300 hover:shadow-md border border-white/20;
  }

  .card-interactive {
    @apply bg-white/80 backdrop-blur-sm rounded-lg shadow-sm p-6 transition-all duration-300 hover:shadow-lg hover:-translate-y-1 cursor-pointer border border-white/20;
  }

  .input-field {
    @apply w-full px-4 py-3 border border-white/30 bg-white/80 backdrop-blur-sm rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300 hover:border-primary-300;
  }

  .spotlight {
    @apply relative;
  }

  .spotlight::before {
    content: '';
    @apply absolute -inset-2 bg-gradient-to-r from-primary-500/20 via-primary-400/30 to-primary-500/20 rounded-xl blur-sm opacity-0 transition-opacity duration-500;
  }

  .spotlight:hover::before {
    @apply opacity-100;
  }

  .spotlight > * {
    @apply relative;
  }

  .glow-effect {
    @apply relative;
  }

  .glow-effect::before {
    content: '';
    @apply absolute -inset-1 bg-gradient-to-r from-primary-500 via-primary-400 to-primary-500 rounded-lg opacity-0 blur transition-all duration-500;
  }

  .glow-effect:hover::before {
    @apply opacity-30 blur-md;
  }

  .gradient-border {
    @apply relative bg-gradient-to-r from-primary-500 to-primary-400 p-0.5 rounded-lg;
  }

  .gradient-border > * {
    @apply bg-white rounded-lg;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-500 to-primary-400 bg-clip-text text-transparent;
  }

  .text-gradient-animated {
    @apply bg-gradient-to-r from-primary-500 via-primary-400 to-primary-500 bg-clip-text text-transparent;
    background-size: 200% 100%;
    animation: gradient-shift 3s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-shimmer {
    animation: shimmer 2s linear infinite;
  }

  /* Glass-morphic styles for authentication */
  .glass-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 25px 45px rgba(0, 0, 0, 0.1);
  }

  .glass-input {
    @apply w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-primary-500/50 focus:border-transparent transition-all duration-300;
  }

  .glass-button {
    background: linear-gradient(135deg, rgba(255, 90, 95, 0.8) 0%, rgba(255, 124, 130, 0.8) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
  }

  .glass-button:hover:not(:disabled) {
    background: linear-gradient(135deg, rgba(255, 90, 95, 0.9) 0%, rgba(255, 124, 130, 0.9) 100%);
    box-shadow: 0 10px 25px rgba(255, 90, 95, 0.3);
    transform: translateY(-2px);
  }

  .glass-button:active:not(:disabled) {
    transform: translateY(0px);
    box-shadow: 0 5px 15px rgba(255, 90, 95, 0.2);
  }

  /* Animated gradient text */
  .text-gradient-animated {
    background: linear-gradient(45deg, #ff5a5f, #7c3aed, #3b82f6, #ec4899);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease infinite;
  }

  @keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-neutral-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-neutral-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-neutral-700;
}

/* Loading animations */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s steps(4, end) infinite;
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  from { box-shadow: 0 0 20px rgba(255, 90, 95, 0.3); }
  to { box-shadow: 0 0 30px rgba(255, 90, 95, 0.6); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Particle animation */
@keyframes particle-float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

/* Ripple effect */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}
