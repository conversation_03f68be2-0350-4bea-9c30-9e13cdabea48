import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowLeft, Play, Tag as TagIcon, Clock, Download, Share, Trash2 } from 'lucide-react';
import { useVideo } from '../contexts/VideoContext';
import LoadingSpinner from '../components/LoadingSpinner';
import type { Video, Tag } from '../contexts/VideoContext';

const VideoDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { getVideo, deleteVideo } = useVideo();
  const [video, setVideo] = useState<Video | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const fetchVideo = async () => {
      if (!id) return;
      
      try {
        setLoading(true);
        const videoData = await getVideo(id);
        setVideo(videoData);
      } catch (err: any) {
        setError(err.message || 'Failed to load video');
      } finally {
        setLoading(false);
      }
    };

    fetchVideo();
  }, [id, getVideo]);

  const handleDelete = async () => {
    if (!video || !window.confirm('Are you sure you want to delete this video?')) return;
    
    try {
      await deleteVideo(video.id);
      navigate('/dashboard');
    } catch (err: any) {
      alert('Failed to delete video: ' + err.message);
    }
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatDuration = (seconds?: number): string => {
    if (!seconds) return '--:--';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const groupTagsByType = (tags: Tag[]) => {
    return tags.reduce((acc, tag) => {
      if (!acc[tag.type]) {
        acc[tag.type] = [];
      }
      acc[tag.type].push(tag);
      return acc;
    }, {} as Record<string, Tag[]>);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-20">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error || !video) {
    return (
      <div className="card text-center py-12">
        <p className="text-red-600 mb-4">{error || 'Video not found'}</p>
        <button
          onClick={() => navigate('/dashboard')}
          className="btn-primary"
        >
          Back to Dashboard
        </button>
      </div>
    );
  }

  const groupedTags = groupTagsByType(video.tags);

  return (
    <div className="space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <button
          onClick={() => navigate('/dashboard')}
          className="flex items-center space-x-2 text-neutral-700 hover:text-primary-500 transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
          <span>Back to Dashboard</span>
        </button>

        <div className="flex items-center space-x-2">
          <button className="p-2 text-neutral-700 hover:text-primary-500 transition-colors">
            <Share className="w-5 h-5" />
          </button>
          <button className="p-2 text-neutral-700 hover:text-primary-500 transition-colors">
            <Download className="w-5 h-5" />
          </button>
          <button
            onClick={handleDelete}
            className="p-2 text-neutral-700 hover:text-red-600 transition-colors"
          >
            <Trash2 className="w-5 h-5" />
          </button>
        </div>
      </motion.div>

      <div className="grid lg:grid-cols-3 gap-8">
        {/* Video Player */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="lg:col-span-2"
        >
          <div className="card">
            <div className="aspect-video bg-neutral-900 rounded-lg overflow-hidden mb-4">
              <video
                controls
                className="w-full h-full"
                poster={video.thumbnailUrl}
              >
                <source src={video.url} type="video/mp4" />
                Your browser does not support the video tag.
              </video>
            </div>

            <div className="space-y-4">
              <div>
                <h1 className="text-2xl font-semibold text-neutral-900 mb-2">
                  {video.title}
                </h1>
                {video.description && (
                  <p className="text-neutral-700">{video.description}</p>
                )}
              </div>

              <div className="flex items-center space-x-6 text-sm text-neutral-700">
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>{formatDate(video.uploadDate)}</span>
                </div>
                {video.duration && (
                  <div className="flex items-center space-x-1">
                    <Play className="w-4 h-4" />
                    <span>{formatDuration(video.duration)}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Transcript */}
          {video.transcript && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="card"
            >
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-neutral-900">
                  Transcript
                </h2>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-neutral-600">
                    Language: {video.transcript?.language || 'original'}
                  </span>
                  <button
                    onClick={() => {
                      // TODO: Add re-transcription with different language
                      alert('Language switching feature coming soon!');
                    }}
                    className="text-sm text-primary-600 hover:text-primary-700"
                  >
                    Switch Language
                  </button>
                </div>
              </div>
              <div className="bg-neutral-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                <p className="text-neutral-700 leading-relaxed whitespace-pre-wrap">
                  {video.transcript?.fullText || 'No transcript available'}
                </p>
              </div>
            </motion.div>
          )}
        </motion.div>

        {/* Sidebar */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="space-y-6"
        >
          {/* Processing Status */}
          <div className="card">
            <h3 className="font-semibold text-neutral-900 mb-4">Processing Status</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-neutral-700">Transcription</span>
                <span className={`text-xs px-2 py-1 rounded-full ${
                  video.transcriptionStatus === 'completed' 
                    ? 'bg-green-100 text-green-800'
                    : video.transcriptionStatus === 'processing'
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-neutral-100 text-neutral-800'
                }`}>
                  {video.transcriptionStatus}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-neutral-700">Vision Analysis</span>
                <span className={`text-xs px-2 py-1 rounded-full ${
                  video.visionStatus === 'completed' 
                    ? 'bg-green-100 text-green-800'
                    : video.visionStatus === 'processing'
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-neutral-100 text-neutral-800'
                }`}>
                  {video.visionStatus}
                </span>
              </div>
            </div>
          </div>

          {/* Tags */}
          {video.tags.length > 0 && (
            <div className="card">
              <h3 className="font-semibold text-neutral-900 mb-4 flex items-center space-x-2">
                <TagIcon className="w-5 h-5" />
                <span>AI Tags ({video.tags.length})</span>
              </h3>
              
              <div className="space-y-4">
                {Object.entries(groupedTags).map(([type, tags]) => (
                  <div key={type}>
                    <h4 className="text-sm font-medium text-neutral-700 mb-2 capitalize">
                      {type}s
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {(tags as Tag[]).map((tag: Tag) => (
                        <span
                          key={tag.id}
                          className="inline-flex items-center px-2 py-1 bg-primary-100 text-primary-800 text-xs rounded-full"
                        >
                          {tag.label}
                          {tag.confidence && (
                            <span className="ml-1 text-primary-600">
                              {Math.round(tag.confidence * 100)}%
                            </span>
                          )}
                        </span>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Video Info */}
          <div className="card">
            <h3 className="font-semibold text-neutral-900 mb-4">Video Information</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-neutral-700">Upload Date</span>
                <span className="text-neutral-900">{formatDate(video.uploadDate)}</span>
              </div>
              {video.duration && (
                <div className="flex justify-between">
                  <span className="text-neutral-700">Duration</span>
                  <span className="text-neutral-900">{formatDuration(video.duration)}</span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-neutral-700">Status</span>
                <span className="text-neutral-900">
                  {video.transcriptionStatus === 'completed' && video.visionStatus === 'completed'
                    ? 'Ready'
                    : 'Processing'
                  }
                </span>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default VideoDetail;
