import React from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import { LogOut, Video, Search, Sparkles, User, Settings } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
  };

  const handleProfileClick = () => {
    navigate('/settings');
  };

  return (
    <div className="min-h-screen">
      {/* Header */}
      <header className="backdrop-blur-md border-b sticky top-0 z-50 bg-black/30 border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <motion.div 
              className="flex items-center space-x-2"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="w-8 h-8 rounded-lg flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-400">
                <img src="/footage-flow.png" alt="FootageFlow" className="w-6 h-6" />
              </div>
              <h1 className="text-xl font-semibold text-white">
                Footage<span className="bg-gradient-to-r from-blue-300 to-purple-300 bg-clip-text text-transparent">Flow</span>
              </h1>
            </motion.div>

            {/* Navigation */}
            <nav className="hidden md:flex items-center space-x-8">
              <a
                href="/dashboard"
                className="transition-colors duration-200 flex items-center space-x-1 text-white/80 hover:text-white"
              >
                <Video className="w-4 h-4" />
                <span>Videos</span>
              </a>
              <a
                href="/search"
                className="transition-colors duration-200 flex items-center space-x-1 text-white/80 hover:text-white"
              >
                <Search className="w-4 h-4" />
                <span>Search</span>
              </a>
              <a
                href="/stories"
                className="transition-colors duration-200 flex items-center space-x-1 text-white/80 hover:text-white"
              >
                <Sparkles className="w-4 h-4" />
                <span>Stories</span>
              </a>
            </nav>

            {/* User Menu */}
            <motion.div
              className="flex items-center space-x-4"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
            >
              <button
                onClick={handleProfileClick}
                className="flex items-center space-x-3 p-2 rounded-lg transition-all duration-200 hover:bg-white/10"
                title="Profile Settings"
              >
                {user?.avatar ? (
                  <img
                    src={user.avatar}
                    alt={user.name || user.email}
                    className="w-8 h-8 rounded-full border-2 border-white/20 hover:border-white/40 transition-colors"
                  />
                ) : (
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-semibold text-sm">
                    {(user?.name || user?.email || 'U').charAt(0).toUpperCase()}
                  </div>
                )}
                <div className="hidden sm:block text-left">
                  <p className="text-sm font-medium text-white">
                    {user?.name || user?.email}
                  </p>
                  <p className="text-xs text-white/60">
                    View profile
                  </p>
                </div>
              </button>

              <button
                onClick={handleProfileClick}
                className="p-2 transition-colors duration-200 text-white/80 hover:text-white"
                title="Settings"
              >
                <Settings className="w-5 h-5" />
              </button>

              <button
                onClick={handleLogout}
                className="p-2 transition-colors duration-200 text-white/80 hover:text-white"
                title="Logout"
              >
                <LogOut className="w-5 h-5" />
              </button>
            </motion.div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
        >
          {children}
        </motion.div>
      </main>

      {/* Footer */}
      <footer className="backdrop-blur-md border-t mt-20 bg-black/30 border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <p className="text-sm text-white/70">
              © 2024 FootageFlow. Built with ❤️ for video creators.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Layout;
