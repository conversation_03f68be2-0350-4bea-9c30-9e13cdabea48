import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import axios from 'axios';

export interface Tag {
  id: string;
  label: string;
  confidence?: number;
  type: string;
  timestamp?: number;
}

export interface Transcript {
  id: string;
  fullText: string;
  segments?: any[];
  language?: string;
  createdAt: string;
}

export interface Video {
  id: string;
  title: string;
  description?: string;
  url: string;
  thumbnailUrl?: string;
  duration?: number;
  uploadDate: string;
  transcriptionStatus: string;
  visionStatus: string;
  transcript?: Transcript;
  transcriptTimestamps?: any;
  tags: Tag[];
}

interface VideoContextType {
  videos: Video[];
  loading: boolean;
  error: string | null;
  uploadProgress: number;
  fetchVideos: () => Promise<void>;
  uploadVideo: (file: File, title?: string, description?: string) => Promise<Video>;
  deleteVideo: (id: string) => Promise<void>;
  updateVideo: (id: string, updates: Partial<Video>) => Promise<Video>;
  searchVideos: (query: string) => Promise<Video[]>;
  getVideo: (id: string) => Promise<Video>;
}

const VideoContext = createContext<VideoContextType | undefined>(undefined);

export const VideoProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);

  const fetchVideos = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await axios.get('/api/videos');
      setVideos(response.data);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch videos');
      console.error('Error fetching videos:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const uploadVideo = useCallback(async (file: File, title?: string, description?: string): Promise<Video> => {
    try {
      setLoading(true);
      setError(null);
      setUploadProgress(0);

      const formData = new FormData();
      formData.append('video', file);
      if (title) formData.append('title', title);
      if (description) formData.append('description', description);

      const response = await axios.post('/api/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 300000, // 5 minutes timeout
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            setUploadProgress(percentCompleted);
            console.log(`Upload progress: ${percentCompleted}%`);
          }
        },
      });

      const newVideo = response.data.video;
      setVideos(prev => [newVideo, ...prev]);
      setUploadProgress(100);
      return newVideo;
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || 'Failed to upload video';
      setError(errorMessage);
      setUploadProgress(0);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteVideo = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      await axios.delete(`/api/videos/${id}`);
      setVideos(prev => prev.filter(video => video.id !== id));
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to delete video');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateVideo = useCallback(async (id: string, updates: Partial<Video>): Promise<Video> => {
    try {
      setLoading(true);
      setError(null);
      const response = await axios.put(`/api/videos/${id}`, updates);
      const updatedVideo = response.data;
      
      setVideos(prev => prev.map(video => 
        video.id === id ? updatedVideo : video
      ));
      
      return updatedVideo;
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to update video');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const searchVideos = useCallback(async (query: string): Promise<Video[]> => {
    try {
      setLoading(true);
      setError(null);
      const response = await axios.get(`/api/videos/search?query=${encodeURIComponent(query)}`);
      return response.data;
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to search videos');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const getVideo = useCallback(async (id: string): Promise<Video> => {
    try {
      setLoading(true);
      setError(null);
      const response = await axios.get(`/api/videos/${id}`);
      return response.data;
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch video');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const value: VideoContextType = {
    videos,
    loading,
    error,
    uploadProgress,
    fetchVideos,
    uploadVideo,
    deleteVideo,
    updateVideo,
    searchVideos,
    getVideo,
  };

  return (
    <VideoContext.Provider value={value}>
      {children}
    </VideoContext.Provider>
  );
};

export const useVideo = (): VideoContextType => {
  const context = useContext(VideoContext);
  if (context === undefined) {
    throw new Error('useVideo must be used within a VideoProvider');
  }
  return context;
};
