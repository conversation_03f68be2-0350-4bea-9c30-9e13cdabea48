<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/footage-flow.ico" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/footage-flow.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#3b82f6" />
    <meta
      name="description"
      content="AI-powered video management and story generation platform"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/footage-flow.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Preconnect to external domains for better performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Inter font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <title>FootageFlow - AI Video Management</title>
    
    <style>
      /* Loading screen styles */
      #loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: opacity 0.5s ease-out;
      }
      
      #loading-screen.fade-out {
        opacity: 0;
        pointer-events: none;
      }
      
      .loading-logo {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 24px;
        animation: pulse 2s ease-in-out infinite;
      }
      
      .loading-text {
        color: white;
        font-family: 'Inter', sans-serif;
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 8px;
      }
      
      .loading-subtitle {
        color: rgba(255, 255, 255, 0.8);
        font-family: 'Inter', sans-serif;
        font-size: 16px;
        font-weight: 400;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-top: 24px;
      }
      
      @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Hide scrollbar during loading */
      body.loading {
        overflow: hidden;
      }
    </style>
  </head>
  <body class="loading">
    <noscript>You need to enable JavaScript to run this app.</noscript>
    
    <!-- Loading Screen -->
    <div id="loading-screen">
      <div class="loading-logo">
        <img src="/footage-flow.png" alt="FootageFlow" style="width: 48px; height: 48px;" />
      </div>
      <div class="loading-text">FootageFlow</div>
      <div class="loading-subtitle">AI-Powered Video Management</div>
      <div class="loading-spinner"></div>
    </div>
    
    <div id="root"></div>
    
    <script>
      // Hide loading screen when React app loads
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingScreen = document.getElementById('loading-screen');
          const body = document.body;
          
          if (loadingScreen) {
            loadingScreen.classList.add('fade-out');
            body.classList.remove('loading');
            
            setTimeout(function() {
              loadingScreen.remove();
            }, 500);
          }
        }, 1000); // Show loading for at least 1 second
      });
      
      // Fallback: hide loading screen after 5 seconds
      setTimeout(function() {
        const loadingScreen = document.getElementById('loading-screen');
        const body = document.body;
        
        if (loadingScreen) {
          loadingScreen.classList.add('fade-out');
          body.classList.remove('loading');
          
          setTimeout(function() {
            loadingScreen.remove();
          }, 500);
        }
      }, 5000);
    </script>
  </body>
</html>
